<template>
  <view class="attachment_container">
    <view class="attachment_label">
      <view class="label">附件</view>
      <lsj-upload
        v-show="!disabled && list.length < maxLimit"
        ref="lsjUpload1"
        childId="upload1"
        :option="option"
        :size="5"
        :count="maxLimit"
        :formats="formats"
        :debug="false"
        :instantly="true"
        height="24px"
        @change="() => {}"
        @progress="() => {}"
        @uploadEnd="item => attachmentUploadEnd(item, 'files')"
      >
        <view class="add_btn" style="padding-right: 30px">上传附件</view>
      </lsj-upload>
      <text class="invoice_count">{{ list.length }}/{{ maxLimit }}</text>
    </view>
    <view style="padding: 10px">
      <upload-files
        :readonly="disabled"
        :fileType="3"
        :files="getPreFiles()"
        :noData="false"
        @resetUpload="name => resetUpload('lsjUpload1', name)"
        @clear="name => clearUpload('lsjUpload1', name)"
      />
    </view>
  </view>
</template>

<script>
import uploaderMixins from "@/mixins/uploader.js";

export default {
  mixins: [uploaderMixins],
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    maxLimit: {
      type: Number,
      default: 20,
    },
    formats: {
      type: String,
      default: "jpg,png,jpeg,pdf",
    },
  },
  data() {
    return {
      list: [],
      // 文件回显列表
      files: new Map(),
    };
  },
  watch: {
    value: {
      handler(val) {
        this.list = this._.cloneDeep(val);
        setTimeout(() => {
          const uploadRef = this.$refs.lsjUpload1;
          if (uploadRef) {
            uploadRef.setFiles(this._.cloneDeep(val));
            this.files = uploadRef.getFiles();
            this.$forceUpdate();
          }
        }, 300);
        // const fileList = this._.cloneDeep(val).map(s => {
        //   s.name = s.FileName || s?.file?.name || s.Id;
        //   return [s.name, s];
        // });
        // this.files = new Map(fileList);
      },
      immediate: true,
    },
  },
  methods: {
    updateDomStyle() {
      this.$refs.lsjUpload1 && this.$refs.lsjUpload1.updateDomStyle();
    },
    change() {
      const list = this._.cloneDeep(this.getAttachmentList());
      this.$emit("input", list);
      this.$emit("change", list);
      this.$forceUpdate();
    },
    attachmentUploadEnd(item, filesField = "files") {
      this[filesField].set(item.name, item);
      this.change();
    },
    getAttachmentList() {
      let result = [];
      if (this.files.size > 0) {
        result = [...this.files.values()].filter(s => s);
      }
      return result;
    },
    clearUpload(uploadRef, name) {
      this.$refs[uploadRef].clear(name);
      this.change();
    },
  },
};
</script>

<style lang="scss" scoped>
.attachment_container {
  padding-top: 5px;
  .attachment_label {
    display: flex;
    align-items: center;
    position: relative;
    .label {
      flex-shrink: 0;
      color: #606266;
    }
    .add_btn {
      flex: 1;
      text-align: right;
      color: $uni-primary;
      margin-right: 10px;
    }
    .invoice_count {
      position: absolute;
      top: 0;
      right: 0;
      color: $uni-secondary-color;
    }
  }
}
</style>
